{"common": {"confirm": "确定", "cancel": "取消", "save": "保存", "delete": "删除", "edit": "编辑", "add": "新增", "search": "搜索", "reset": "重置", "submit": "提交", "back": "返回", "next": "下一步", "previous": "上一步", "loading": "加载中...", "success": "操作成功", "error": "操作失败", "warning": "警告", "info": "提示", "yes": "是", "no": "否", "close": "关闭", "refresh": "刷新", "export": "导出", "import": "导入", "view": "查看", "detail": "详情", "operation": "操作", "status": "状态", "createTime": "创建时间", "updateTime": "更新时间", "remark": "备注", "total": "共 {{count}} 条", "page": "第 {{current}} 页，共 {{total}} 页", "createinquiry": "新建询价", "quickinquiry": "快捷提取询价", "all": "全部", "notquoted": "未报价", "closed": "已成交", "notclosed": "未成交", "shippingmethod": "货运方式", "cargotype": "货物类型", "companyname": "客户名", "inquirer": "询价人", "originport": "起始港", "destinationport": "目的港", "grossweight": "货物毛重", "cargovolume": "货物体积", "singlemaxweight": "单件货最大重量", "specialcargo": "特殊货物", "manualquotation": "人工报价", "systemUpdate": "系统更新", "updateDetected": "检测到系统有新版本，建议刷新页面以获得最佳体验。", "refreshNow": "立即刷新", "remindLater": "稍后提醒", "specialmanagement": "特殊报价管理", "inquiry": "询价", "successd": "成功", "failure": "失败", "unknownerror": "未知错误", "basicinformation": "基本信息", "companynamein": "请输入客户名", "inquirytimein": "请选择询价时间", "inquirerin": "请输入询价人", "tradeterms": "贸易术语", "cargoinformation": "货物信息", "shippingrequirements": "运输要求", "specialrequirements": "特殊需求", "user": "用户", "checkNetworkConnection": "请检查网络连接", "submitError": "提交错误:", "formValidationError": "表单验证错误:", "formValidationFailed": "表单验证失败，请检查输入", "pleaseSelectInquiryTime": "请选择询价时间", "pleaseSelectOriginPort": "请选择起始港", "pleaseSelectDestinationPort": "请选择卸货港", "pleaseInputGrossWeight": "请输入毛重", "pleaseSelectShipmentDate": "请选择发货日期"}, "layout": {"systemTitle": "报价管理系统", "profile": "个人信息", "changePassword": "修改密码", "logout": "退出登录", "deactivateAccount": "注销账号", "logoutConfirm": "确定要退出登录吗？", "logoutContent": "退出后需要重新登录才能使用系统", "deactivateConfirm": "确定要注销账号吗？", "deactivateContent": "注销后账号将被永久删除，所有数据将无法恢复，请谨慎操作！", "confirmDeactivate": "确定注销", "logoutSuccess": "已成功退出登录", "deactivateSuccess": "账号已成功注销"}, "menu": {"quotation": "询价管理", "inquiryTime": "询价时间", "supplyPrice": "供应价格筛选", "manualQuotation": "特殊报价", "aiQuotation": "AI智能报价", "IntelligentyQuotation": "智能询价", "personalQuotation": "个人报价", "priceManagement": "价格管理", "domesticPrice": "国内价格", "internationalPrice": "国际价格", "dataManagement": "基础数据管理", "airlineManagement": "航司管理", "harborManagement": "港口管理", "organizationManage": "组织管理", "packageTypeManagement": "包装类型管理", "specialItemsManagement": "特殊物品管理", "shipmentPlaceManagement": "发货地管理", "supplierManagement": "供应商管理", "miscellaneousFeeManagement": "其他费用管理"}, "form": {"required": "此字段为必填项", "email": "请输入正确的邮箱地址", "phone": "请输入正确的手机号码", "password": "密码长度至少6位", "confirmPassword": "两次输入的密码不一致", "pleaseInput": "请输入", "pleaseSelect": "请选择", "inputPlaceholder": "请输入{{field}}", "selectPlaceholder": "请选择{{field}}"}, "table": {"noData": "暂无数据", "actions": "操作", "serialNumber": "序号", "total": "共 {{total}} 条记录"}, "login": {"title": "用户登录", "account": "账号", "accountPlaceholder": "请输入账号", "email": "邮箱", "password": "密码", "login": "登录", "LoginIn": "登录中...", "register": "注册", "forgotPassword": "忘记密码？", "loginSuccess": "登录成功", "loginFailed": "登录失败，请检查用户名和密码", "loginRequestFailed": "登录请求失败，请稍后再试", "emailRequired": "请输入邮箱", "passwordRequired": "请输入密码", "invalidEmail": "请输入正确的邮箱格式", "passwordTooShort": "密码长度不能少于6位", "RegistrationSuccess": "注册成功，请登录", "RegistrationFailed": "注册失败，请稍后再试", "inputRequired": "请输入账号和密码", "noAccount": "还没有账号？", "RegisterNow": "立即注册"}, "register": {"title": "用户注册", "emailLabel": "邮箱", "emailRequired": "请输入邮箱", "emailInvalid": "请输入有效的邮箱地址", "fullnameLabel": "姓名", "fullnameMaxLength": "姓名长度不能超过50个字符", "fullnamePlaceholder": "请输入姓名", "telephoneLabel": "电话", "telephonePlaceholder": "请输入电话", "telephoneInvalid": "请输入有效的手机号码", "passwordLabel": "密码", "passwordRequired": "请输入密码", "passwordMaxLength": "密码长度不能超过20个字符", "confirmPasswordLabel": "确认密码", "confirmPasswordRequired": "请确认密码", "confirmPasswordMismatch": "两次输入的密码不一致", "verifyCodeLabel": "验证码", "verifyCodeRequired": "请输入验证码", "verifyCodeLength": "验证码为6位数字", "sendCode": "获取验证码", "sendCodeCountdown": "{{countdown}}秒后重发"}, "profile": {"title": "个人信息管理", "basicInfo": "基本信息", "email": "邮箱", "name": "姓名", "phone": "手机号", "company": "公司", "department": "部门", "position": "职位", "updateSuccess": "个人信息更新成功", "updateFailed": "个人信息更新失败"}, "password": {"title": "修改密码", "oldPassword": "原密码", "newPassword": "新密码", "confirmPassword": "确认新密码", "changeSuccess": "密码修改成功", "changeFailed": "密码修改失败", "oldPasswordRequired": "请输入原密码", "newPasswordRequired": "请输入新密码", "confirmPasswordRequired": "请确认新密码", "passwordMismatch": "两次输入的密码不一致", "passwordTooShort": "密码长度不能少于6位"}, "verification": {"title": "验证身份", "sendCode": "获取验证码", "inputCode": "请输入验证码", "codeSent": "验证码已发送到您的邮箱", "codeSendFailed": "验证码发送失败", "codeRequired": "请输入6位验证码", "codeInvalid": "验证码错误", "resendAfter": "{{seconds}}秒后重新获取", "sending": "发送中...", "emailHint": "请输入发送到您邮箱 ({{email}}) 的验证码以确认注销账号：", "emailContent": "请输入有效的邮箱地址"}, "supplyPrice": {"title": "供应价格筛选", "inquiryInfo": "询价信息", "selectedInquiry": "已选择询价单", "noInquirySelected": "未选择询价单", "pleaseSelectInquiry": "请先从询价管理页选择一个询价单", "inquiryNumber": "询价单编号", "filterConditions": "筛选条件", "requiredFilters": "必填筛选条件", "coreFilters": "核心筛选条件", "basicFilters": "基础筛选条件", "advancedFilters": "高级筛选条件", "expandAdvanced": "展开高级筛选", "collapseAdvanced": "收起高级筛选", "applyFilter": "应用筛选", "reset": "重置", "selectAirline": "选择航司", "selectOriginPorts": "选择起始港", "selectDates": "选择离港日期", "selectTransferType": "选择航班类型", "transferTypes": {"direct": "直飞", "transfer": "转飞"}, "lowestPrice": "最低价格", "fastestTransit": "最快时效", "noDataMessage": "暂无数据，请调整筛选条件", "loadingMessage": "加载中...", "getSupplyPriceListFailed": "获取供应价格列表失败", "queryFailed": "查询失败，请检查网络连接", "pleaseCompleteRequired": "请填写必填项后再应用筛选", "fields": {"originPort": "起始港", "destinationPort": "目的港", "grossWeight": "货物毛重", "goodsVolume": "货物体积", "singleMaxWeight": "单件货最大重量", "freightMethod": "运输方式", "goodsType": "货物类型", "shippedPlace": "发货地", "cargoSize": "货物尺寸", "cargoLength": "货物长度", "cargoWidth": "货物宽度", "cargoHeight": "货物高度", "isBrand": "是否品牌货", "ensureCabin": "是否可保舱", "requireETD": "是否要求ETD", "shipmentDate": "发货日期", "packageType": "包装形式", "specialCargo": "特殊货物", "NumPackages": "整理件数", "sanctioned": "受制裁", "shippingDate": "发货日期"}, "placeholders": {"selectOriginPort": "请选择起始港", "selectDestinationPort": "请选择目的港", "inputGrossWeight": "请输入货物毛重", "inputGoodsVolume": "请输入货物体积", "inputSingleMaxWeight": "请输入单件货最大重量", "inputnumpackages": "请输入整理件数", "selectFreightMethod": "请选择运输方式", "selectGoodsType": "请选择货物类型", "inputShippedPlace": "请输入发货地", "inputCargoLength": "请输入货物长度", "inputCargoWidth": "请输入货物宽度", "inputCargoHeight": "请输入货物高度", "selectShipmentDate": "请选择发货日期", "selectPackageType": "请选择包装形式", "selectSpecialCargo": "请选择特殊货物", "inputcompanyname": "请输入客户名", "inputinquirer": "请输入询价人", "inputtradeterms": "请选择贸易术语"}, "validation": {"originPortRequired": "请选择起始港", "destinationPortRequired": "请选择目的港", "grossWeightRequired": "请输入货物毛重", "shipmentDateRequired": "请选择发货日期"}, "freightMethods": {"air": "空运", "sea": "海运", "rail": "铁路"}, "goodsTypes": {"fcl": "普通货 (FCL)", "lcl": "拼货 (LCL)"}, "sectionTitles": {"cargoSize": "货物尺寸", "specialOptions": "特殊选项"}}, "priceCard": {"features": {"cabin": "保舱", "transfer": "中转", "packaging": "包装费", "specialItems": "特殊物品", "acceptSanction": "制裁"}, "fields": {"unitPrice": "运费单价", "transitTime": "时效", "nearestFlight": "近期航班", "totalPrice": "总价", "generateQuote": "生成报价", "flightFrequency": "班次频率", "weightCharge": "计费重", "etdCompliant": "符合ETD", "densityRange": "密度范围", "canEnsureCabin": "是否可保舱", "sizeLimit": "尺寸限制", "priceGradient": "价格梯度", "packagingCharges": "包装费用", "specialItemCharges": "特殊物品费用", "length": "长", "width": "宽", "height": "高", "yes": "是", "no": "否", "canEnsure": "可保舱", "cannotEnsure": "不可保舱", "days": "天"}, "weightNote": "（{{weight}}千克以内）", "placeholders": {"length": "长度", "width": "宽度", "height": "高度"}}, "quotationTable": {"columns": {"inquiryTime": "询价时间", "freightMethod": "货运方式", "shippedPlace": "发货地", "originPort": "起始港", "destinationPort": "目的港", "goodsType": "货物类型", "grossWeight": "货物毛重", "goodsVolume": "货物体积", "singleMaxWeight": "单件货最大重量", "cargoSize": "货物尺寸", "companyName": "客户名", "inquirer": "询价人", "isBrand": "品牌货", "ensureCabin": "保舱", "requireETD": "要求ETD", "shipmentDate": "发货日期", "packageType": "包装形式", "specialCargo": "特殊货物", "status": "状态", "actions": "操作", "id": "编号"}, "filters": {"selectShippedPlace": "请选择发货地", "selectOriginPort": "请选择起始港", "selectDestinationPort": "请选择目的港", "inputCompanyName": "输入客户名", "inputInquirer": "输入询价人", "inputInquiryCode": "请输入询价编号"}, "status": {"notQuoted": "未报价", "deal": "已成交", "noDeal": "未成交", "unknown": "未知"}, "actions": {"edit": "编辑", "delete": "删除", "queryPrice": "查询报价", "manualQuote": "人工报价"}, "confirmDelete": {"title": "确定要删除此询价单吗？", "description": "此操作不可逆，请谨慎操作。", "okText": "确定", "cancelText": "取消"}, "goodsTypes": {"fcl": "FCL", "lcl": "LCL"}, "booleanValues": {"yes": "是", "no": "否"}, "noValue": "无", "noData": "-", "messages": {"deleteSuccess": "询价删除成功", "deleteFailed": "删除失败", "getListFailed": "获取询价列表失败", "manualQuoteSuccess": "已加入特殊报价列表，请前往特殊报价管理查看！", "getDepartmentUsersFailed": "获取部门用户列表失败:"}, "placeholders": {"selectDepartmentMember": "选择部门成员", "allDepartmentMembers": "全部部门成员"}}, "emailExtractModal": {"title": "快捷提取询价信息", "steps": {"inputContent": "输入提取内容", "confirmInfo": "确认信息", "createQuotation": "创建询价"}, "stepCards": {"inputContentTitle": "请输入提取内容", "confirmInfoTitle": "确认提取的信息", "createQuotationTitle": "创建询价"}, "alerts": {"inputTip": "提示", "inputDescription": "请将包含询价信息的邮件内容粘贴到下方文本框中，AI将自动提取相关的货运信息。", "confirmTitle": "信息提取完成", "confirmDescription": "请检查下方提取的信息是否正确，可以直接编辑修改。", "multiplePortsDetected": "检测到多个目的港，将创建 {{count}} 条询价记录", "portListLabel": "目的港列表：", "editFirstRecordTip": "您可以在下方编辑第一条记录的信息，其他记录将使用相同的基础信息。"}, "placeholders": {"emailContent": "请粘贴邮件内容或询价信息...", "companyName": "客户名称", "inquirer": "询价人", "shippedPlace": "发货地", "originPort": "起始港", "destinationPort": "目的港", "grossWeight": "毛重", "goodsVolume": "体积", "goodsNumber": "整理件数", "cargoLength": "长度", "cargoWidth": "宽度", "cargoHeight": "高度", "packageType": "包装类型", "specialCargo": "特殊货物要求"}, "formLabels": {"basicInfo": "基本信息", "cargoInfo": "货物信息", "companyName": "客户名称", "inquirer": "询价人", "shippedPlace": "发货地", "originPort": "起始港", "destinationPort": "目的港", "grossWeight": "毛重 (kg)", "goodsVolume": "体积 (CBM)", "goodsNumber": "整理件数", "cargoLength": "长度 (cm)", "cargoWidth": "宽度 (cm)", "cargoHeight": "高度 (cm)", "packageType": "包装类型", "specialCargo": "特殊货物"}, "buttons": {"cancel": "取消", "startExtract": "开始提取信息", "extracting": "AI正在提取信息...", "backToPrevious": "返回上一步", "confirmAndCreate": "确认信息并创建询价", "creating": "正在创建询价..."}, "loadingTexts": {"aiAnalyzing": "AI正在分析内容", "aiAnalyzingDesc": "正在使用AI从您的内容中提取货运询价信息，请稍候...", "analyzingProgress": "正在分析内容并提取关键信息...", "creatingQuotation": "正在创建询价单", "creatingQuotationDesc": "系统正在根据提取的信息创建询价单，请稍候...", "creatingProgress": "即将完成询价单创建..."}, "messages": {"inputRequired": "请输入提取内容", "extractSuccess": "信息提取成功！", "extractFailed": "提取信息失败，请重试", "createSuccess": "创建询价成功", "createMultipleSuccess": "成功创建 {{count}} 条询价记录", "createFailed": "创建询价失败，请重试", "formValidationFailed": "请检查表单信息", "apiKeyInvalid": "DeepSeek API Key无效或未设置，请检查配置", "parseFormatFailed": "AI返回格式解析失败，请重试", "extractFailedWithError": "提取失败: {{error}}"}, "validationMessages": {"selectOriginPort": "请选择起始港", "selectDestinationPort": "请选择卸货港", "inputGrossWeight": "请输入毛重", "selectShipmentDate": "请选择发货日期"}, "formPlaceholders": {"length": "长度", "width": "宽度", "height": "高度"}, "console": {"startFirstRound": "开始第一轮AI对话...", "firstRoundResult": "第一轮AI返回内容:", "startSecondRound": "开始第二轮AI对话...", "secondRoundResult": "第二轮AI返回内容:", "extractFailed": "AI提取信息失败:", "createFailed": "创建询价失败:", "convertShippedPlaceError": "转换发货地 \"{{place}}\" 为拼音时出错:", "parseSecondRoundFailed": "解析第二轮AI响应失败:"}, "errors": {"firstRoundFormatError": "第一轮AI接口返回格式异常", "secondRoundFormatError": "第二轮AI接口返回格式异常", "aiResponseFormatIncorrect": "AI返回格式不正确", "parseSecondRoundFailed": "解析第二轮AI响应失败", "parseKeyword": "解析"}}, "aiCabinExtractor": {"buttonText": "智能提取舱报信息", "modalTitle": "智能提取舱报信息", "extractButton": "提取信息", "inputLabel": "请输入舱报信息文本：", "inputPlaceholder": "请粘贴或输入舱报信息文本，系统将自动识别并提取相关数据...", "tipsTitle": "使用提示", "tips": {"tip1": "支持识别航班日期、重量限制、体积限制等基本信息", "tip2": "自动提取密度范围、价格调整、特价信息", "tip3": "可识别是否仅接受零件、是否单独询价等特殊要求", "tip4": "提取后的信息可进一步手动调整和完善"}, "messages": {"inputRequired": "请输入舱报信息文本", "extractSuccess": "舱报信息提取成功！", "extractFailed": "提取失败，请重试"}, "errors": {"aiResponseError": "解析失败"}}, "internationalPriceManagement": {"title": "国际价格管理", "newPrice": "新建价格", "selectDepartmentMember": "选择部门成员", "user": "用户", "clearAllFilters": "清除筛选", "columns": {"updateDate": "更新日期", "airlineName": "航司名", "originPort": "起始港", "destinationPort": "目的港", "originSchedules": "起始班次", "transferPort": "中转港", "transferSchedules": "中转班次", "supplier": "供应商", "mPrice": "M", "nPrice": "N", "q45Price": "Q45", "q100Price": "Q100", "q300Price": "Q300", "q500Price": "Q500", "q1000Price": "Q1000", "subQ45Price": "Q45靠级", "subQ100Price": "Q100靠级", "densityRange": "密度(kg/m³)", "lengthLimit": "长度限制", "widthLimit": "宽度限制", "heightLimit": "高度限制", "packageCharges": "包装费用", "specialCharges": "特殊物品收费", "isEffective": "是否有效", "effectiveDate": "生效日期", "cabinReport": "舱报信息", "actions": "操作", "id": "编号"}, "filters": {"selectAirline": "请选择航司", "selectOriginPort": "请选择起始港", "selectDestinationPort": "请选择目的港", "inputSupplierName": "输入供应商名称", "selectSupplier": "选择供应商"}, "schedules": {"monday": "星期一", "tuesday": "星期二", "wednesday": "星期三", "thursday": "星期四", "friday": "星期五", "saturday": "星期六", "sunday": "星期天"}, "status": {"effective": "有效", "ineffective": "无效"}, "transfer": {"yes": "中转", "no": "直飞"}, "actions": {"save": "保存", "cancel": "取消", "viewDetail": "查看详情", "copy": "复制", "edit": "编辑", "delete": "删除", "priceIncrease": "价格+1", "priceDecrease": "价格-1", "batchPriceIncrease": "Q100-Q1000价格统一+1", "batchPriceDecrease": "Q100-Q1000价格统一-1"}, "confirmDelete": {"title": "删除价格", "description": "此操作将删除该价格,是否继续?", "okText": "确定", "cancelText": "取消"}, "placeholders": {"scheduleFormat": "格式：D+数字1-7，如D123"}, "cabinReport": {"detailTitle": "舱报信息详情", "totalCount": "共 {{count}} 条", "unknownDate": "未知日期", "unknown": "未知", "badges": {"limit": "限制", "special": "特价", "parts": "散件", "inquiry": "单询"}, "tags": {"limit": "限", "special": "特"}, "labels": {"densityRange": "密度范围", "limitations": "限制条件", "weight": "重量", "volume": "体积", "priceChanges": "价格变化", "specialPrice": "特价找货", "density": "密度"}}, "messages": {"priceUpdateSuccess": "价格更新成功", "priceUpdateFailed": "价格更新失败", "priceDeleteSuccess": "价格删除成功", "deleteFailed": "删除失败", "getListFailed": "获取国际价格列表失败", "formValidationFailed": "表单验证失败", "getDepartmentUsersFailed": "获取部门用户列表失败", "priceIncreaseSuccess": "价格增加成功", "priceDecreaseSuccess": "价格减少成功", "quickAdjustFailed": "快速调整价格失败", "pleaseSelectRows": "请选择要修改的记录", "pleaseInputAdjustValue": "请输入有效的价格（不能小于0）", "batchUpdateSuccess": "批量设置成功，共修改了 {{count}} 条{{type}}记录", "batchUpdateFailed": "批量更新失败", "cabinReportUpdateSuccess": "舱报信息更新成功", "cabinReportUpdateFailed": "舱报信息更新失败", "intervalDeleteSuccess": "删除密度价格区间成功", "clearFiltersSuccess": "已清除所有筛选条件"}, "batchEdit": {"title": "批量编辑", "batchMode": "批量修改模式", "selectedCount": "已选择 {{count}} 条记录", "batchEditButton": "批量编辑", "cancel": "取消", "confirm": "确认修改", "description": "将对选中的 {{count}} 条记录进行批量修改，只需填写要修改的字段：", "pleaseSelectAtLeastOneField": "请至少填写一个要修改的字段", "batchUpdateSuccess": "成功批量修改 {{count}} 条记录", "batchUpdateFailed": "批量更新失败，请重试", "fields": {"mPrice": "M价", "nPrice": "N价", "q45Price": "Q45价", "subQ45Price": "Q45靠级价", "subQ100Price": "Q100靠级价", "originSchedules": "起始班次", "transferSchedules": "中转班次", "isEffective": "是否有效"}, "placeholders": {"inputNewMPrice": "输入新的M价", "inputNewNPrice": "输入新的N价", "inputNewQ45Price": "输入新的Q45价", "inputNewSubQ45Price": "输入新的Q45靠级价", "inputNewSubQ100Price": "输入新的Q100靠级价", "scheduleFormat": "格式：D+数字1-7，如D123", "selectEffectiveStatus": "选择是否有效状态"}, "validation": {"priceCannotBeLessThanZero": "{{field}}不能小于0", "scheduleFormatError": "格式：D+数字1-7，如D123，数字不能重复"}}, "expandedTable": {"title": "密度价格区间详情", "addInterval": "新增区间", "deleteConfirm": {"title": "确认删除", "description": "确定要删除这个密度价格区间吗？", "okText": "确认", "cancelText": "取消"}}, "validation": {"pleaseInput": "请输入{{field}}!", "pleaseInputMPrice": "请输入M价!", "pleaseInputNPrice": "请输入N价!", "pleaseInputQ45Price": "请输入Q45价!", "pleaseInputQ100Price": "请输入Q100价!", "pleaseInputQ300Price": "请输入Q300价!", "pleaseInputQ500Price": "请输入Q500价!", "pleaseInputQ1000Price": "请输入Q1000价!", "scheduleStartWithD": "请以字母D开头", "scheduleOnlyNumbers1to7": "D后只能包含数字1-7", "scheduleNoDuplicateNumbers": "数字不能重复"}, "pagination": {"total": "共 {{total}} 条记录"}, "actionModal": {"title": {"add": "新增国际价格", "edit": "编辑国际价格"}, "buttons": {"back": "返回", "reset": "重置", "cancel": "取消", "save": "保存", "delete": "删除", "addPackageCharge": "添加包装费用", "addSpecialCharge": "添加特殊费用项", "addIntervalPrice": "添加密度价格区间", "addOtherFee": "添加其他费用", "addCabinReport": "添加舱报信息", "addPriceChange": "添加价格变化", "addSpecialPrice": "添加特价找货"}, "sections": {"basicInfo": "基本信息", "routeInfo": "航线信息", "densityAndSize": "尺寸重量限制", "intervalPricing": "密度价格区间", "specialItems": "特殊物品配置", "priceInfo": "价格信息", "packageCharges": "包装费用", "specialCharges": "特殊物品收费价", "tieredPricing": "阶梯价格", "smallCargoFee": "小货操作费", "branchPricing": "分支价格", "otherFees": "其他费用", "cabinReport": "舱报信息", "priceChanges": "价格变化", "specialPrice": "特价找货"}, "fields": {"supplier": "供应商", "effectiveDate": "生效日期", "isEffective": "是否有效", "acceptSanction": "接受制裁", "airlineName": "航司名", "originPort": "起始港", "originSchedules": "起始班次", "destinationPort": "目的港", "isTransfer": "是否中转", "transferPort": "中转港", "transferSchedules": "中转班次", "minDensity": "最小密度", "maxDensity": "最大密度", "lengthLimit": "长度限制", "widthLimit": "宽度限制", "heightLimit": "高度限制", "isCabin": "是否可保舱", "cabinPrice": "保舱价", "mPrice": "M价", "nPrice": "N价", "q45Price": "Q45价", "q100Price": "Q100价", "q300Price": "Q300价", "q500Price": "Q500价", "q1000Price": "Q1000价", "smallCargoFee": "小货操作费", "q45Sub": "M、N向Q45靠拢", "subQ45Price": "向Q45靠拢价", "q100Sub": "Q45向Q100靠拢", "subQ100Price": "向Q100靠拢价", "packageItemName": "包装项名称", "chargeValue": "费用值", "specialItemName": "费用项名称", "cabinReportItem": "舱报信息", "cabinDate": "日期", "transferDate": "中转离港日期", "weightLimit": "重量限制数值", "volumeLimit": "体积限制数值", "lowerDensity": "密度下限数值", "upperDensity": "密度上限数值", "onlyParts": "限散件", "singleInquiry": "单询", "leftDensity": "密度范围：左值", "rightDensity": "密度范围：右值", "priceChange": "价格变化", "densityUpperLimit": "密度上限", "densityLowerLimit": "密度下限", "specialPrice": "特价找货", "densityRange": "密度范围", "singleWeightLimit": "单件重量限制", "acceptBrand": "是否接受品牌货", "feeName": "费用名称", "billingMethod": "计费方式", "orderFees": "每单费用", "weightFees": "每kg费用", "minCharges": "最低收费", "isCharges": "默认收费"}, "placeholders": {"inputSupplier": "请输入供应商", "selectSupplier": "请选择供应商", "selectAirline": "请选择航司名", "selectOriginPort": "请选择起始港", "selectOriginSchedules": "请选择起始班次", "selectDestinationPort": "请选择目的港", "selectTransferPort": "请选择中转港", "selectTransferSchedules": "请选择中转班次", "inputMinDensity": "请输入最小密度", "inputMaxDensity": "请输入最大密度", "inputLengthLimit": "请输入长度限制", "inputWidthLimit": "请输入宽度限制", "inputHeightLimit": "请输入高度限制", "inputMPrice": "请输入M价", "inputNPrice": "请输入N价", "inputQ45Price": "请输入Q45价", "inputQ100Price": "请输入Q100价", "inputQ300Price": "请输入Q300价", "inputQ500Price": "请输入Q500价", "inputQ1000Price": "请输入Q1000价", "inputSmallCargoFee": "请输入小货操作费", "inputSubQ45Price": "请输入向Q45靠拢价", "inputSubQ100Price": "请输入向Q100靠拢价", "packageItemName": "包装项名称", "chargeValue": "费用值", "specialItemName": "费用项名称", "inputWeightLimit": "请输入重量限制，默认99999代表不受限制", "inputVolumeLimit": "请输入体积限制，默认99999代表不受限制", "inputLowerDensity": "请输入密度下限，默认0", "inputUpperDensity": "请输入密度上限，默认99999代表不受限制", "inputLeftDensity": "请输入密度范围左值", "inputRightDensity": "请输入密度范围右值", "inputPriceChange": "请输入价格变化值，负数表示减少", "inputDensityUpperLimit": "请输入密度上限", "inputDensityLowerLimit": "请输入密度下限", "inputSpecialPrice": "请输入特价找货价格", "inputDensityLowerValue": "请输入密度下限", "inputDensityUpperValue": "请输入密度上限", "selectDates": "请选择日期", "minDensity": "最小密度", "maxDensity": "最大密度", "inputSingleWeightLimit": "请输入单件重量限制", "singleWeightLimitHelp": "默认99999kg代表不限制", "inputFeeName": "请选择费用名称", "inputOrderFees": "请输入每单费用", "inputWeightFees": "请输入每kg费用", "inputMinCharges": "请输入最低收费"}, "weekdays": {"all": "全选", "monday": "星期一", "tuesday": "星期二", "wednesday": "星期三", "thursday": "星期四", "friday": "星期五", "saturday": "星期六", "sunday": "星期天"}, "billingMethods": {"perOrder": "每单收费", "perKg": "每kg收费+最低收费"}, "validation": {"selectEffectiveDate": "请选择生效日期", "selectAirline": "请选择航司名", "selectOriginPort": "请选择起始港", "selectOriginSchedules": "请选择起始班次", "selectDestinationPort": "请选择目的港", "selectTransferPort": "请选择中转港", "selectTransferSchedules": "请选择中转班次", "selectCabinDate": "请选择舱报日期", "selectTransferDate": "请选择中转离港日期", "inputMinDensity": "请输入最小密度", "inputMaxDensity": "请输入最大密度", "inputQ100Price": "请输入Q100价格", "inputQ300Price": "请输入Q300价格", "inputQ500Price": "请输入Q500价格", "inputQ1000Price": "请输入Q1000价格", "inputLeftDensity": "请输入密度范围左值", "inputRightDensity": "请输入密度范围右值", "inputPriceChange": "请输入价格变化值", "inputDensityLowerLimit": "请输入密度下限", "inputDensityUpperLimit": "请输入密度上限", "inputSpecialPrice": "请输入特价找货价格", "inputFeeName": "请输入费用名称", "inputOrderFees": "请输入每单费用", "inputWeightFees": "请输入每kg费用", "inputMinCharges": "请输入最低收费"}, "messages": {"addSuccess": "新增成功", "editSuccess": "编辑成功", "addFailed": "新增失败", "editFailed": "编辑失败", "submitFailed": "提交失败，请检查网络连接", "submitError": "提交表单时出错", "validationError": "表单验证失败", "unknownError": "未知错误"}}, "detailModal": {"title": "国际价格详情", "sections": {"basicInfo": "基本信息", "routeInfo": "航线信息", "cargoLimits": "货物限制", "priceInfo": "价格信息", "priceTiers": "价格梯度", "specialCharges": "特殊物品附加费", "packageCharges": "包装费用"}, "fields": {"supplierName": "供应商名称", "createTime": "创建时间", "updateTime": "更新时间", "effectiveTime": "生效时间", "isEffective": "是否有效", "airline": "航空公司", "originPort": "起始港", "originSchedules": "起始班次", "destinationPort": "目的港", "isTransfer": "是否中转", "transferPort": "中转港", "transferSchedules": "中转班次", "minDensity": "最小密度", "maxDensity": "最大密度", "lengthLimit": "长度限制", "widthLimit": "宽度限制", "heightLimit": "高度限制", "singleWeightLimit": "单件重量限制", "acceptBrand": "是否接受品牌货", "isCabin": "是否可保舱", "cabinPrice": "保舱价格", "smallCargoFee": "小货操作费"}, "priceTypes": {"mPrice": "M价", "nPrice": "N价", "q45Price": "Q45价格", "q100Price": "Q100价格", "bulkPrice": "大货价格", "standardPrice": "标准价格", "basePrice": "基础价格", "above45kg": "45kg以上", "above100kg": "100kg以上", "above300kg": "300kg以上"}, "priceLabels": {"standard": "标准价", "base": "基础价", "standardTier": "标准", "segment": "分段", "q300": "Q300", "q500": "Q500", "q1000": "Q1000"}, "booleanValues": {"yes": "是", "no": "否"}, "units": {"cny": "CNY", "cnyPerKg": "CNY/KG", "kgPerCubicMeter": "kg/m³"}, "noData": "-"}, "cabinReportEdit": {"title": "编辑舱报信息"}}, "domesticPriceManagement": {"title": "国内价格管理", "newPrice": "新建价格", "columns": {"shippingPlace": "发货地", "shippingProvince": "发货省份", "destination": "目的地", "volumePrice": "体积价格(元/方)", "weightPrice": "重量价格(元/公斤)", "minCharge": "最低收费(元)", "fastestAging": "最快时效(天)", "slowestAging": "最慢时效(天)", "deliveryFee": "提送货费(元)", "logistics": "物流", "actions": "操作"}, "filters": {"inputLogisticsName": "输入物流名称", "confirm": "确定", "reset": "重置"}, "tooltips": {"viewDetail": "查看详情", "edit": "编辑", "delete": "删除"}, "deleteConfirm": {"title": "删除价格", "description": "此操作将删除该价格,是否继续?", "okText": "确定", "cancelText": "取消"}, "pagination": {"total": "共 {{total}} 条记录"}, "messages": {"fetchListFailed": "获取国内价格列表失败", "deleteSuccess": "价格删除成功", "addSuccess": "价格添加成功", "editSuccess": "价格修改成功", "addFailed": "价格添加失败", "editFailed": "价格修改失败"}, "actionModal": {"title": {"add": "新建国内价格", "edit": "编辑国内价格"}, "sections": {"basicInfo": "基本信息", "priceInfo": "价格信息", "agingInfo": "时效信息"}, "fields": {"shippingPlace": "发货地", "shippingProvince": "发货省份", "destination": "目的地", "volumePrice": "体积价格", "weightPrice": "重量价格", "minCharge": "最低收费", "deliveryFee": "提送货费", "logistics": "物流", "fastestAging": "最快时效", "slowestAging": "最慢时效"}, "placeholders": {"selectShippingPlace": "请选择发货地", "selectShippingProvince": "请选择发货省份", "selectDestination": "请选择目的地", "inputVolumePrice": "请输入体积价格", "inputWeightPrice": "请输入重量价格", "inputMinCharge": "请输入最低收费", "inputDeliveryFee": "请输入提送货费", "inputLogistics": "请输入物流", "inputFastestAging": "请输入最快时效", "inputSlowestAging": "请输入最慢时效"}, "validation": {"shippingPlaceRequired": "请选择发货地!", "shippingProvinceRequired": "请选择发货省份!", "destinationRequired": "请选择目的地!", "volumePriceRequired": "请输入体积价格!", "weightPriceRequired": "请输入重量价格!", "minChargeRequired": "请输入最低收费!", "deliveryFeeRequired": "请输入提送货费!", "logisticsRequired": "请输入物流!", "logisticsMaxLength": "物流不能超过50个字符!", "fastestAgingRequired": "请输入最快时效!", "slowestAgingRequired": "请输入最慢时效!"}, "units": {"yuanPerCubicMeter": "元/方", "yuanPerKg": "元/公斤", "yuan": "元", "days": "天"}}, "detailModal": {"title": "国内价格详情", "sections": {"basicInfo": "基本信息", "priceInfo": "价格信息", "agingInfo": "时效信息"}, "fields": {"shippingPlace": "发货地", "shippingProvince": "发货省份", "destination": "目的地", "logistics": "物流公司", "volumePrice": "体积价格", "weightPrice": "重量价格", "minCharge": "最低收费", "deliveryFee": "送货费", "fastestAging": "最快时效", "slowestAging": "最慢时效"}, "booleanValues": {"yes": "是", "no": "否"}, "units": {"days": "天"}}}, "manualQuotation": {"title": "特殊报价管理", "columns": {"inquiryId": "询价单号", "freightMethod": "货运方式", "goodsType": "货物类型", "tradeTerms": "贸易术语", "companyName": "客户名", "inquirer": "询价人", "inquiryTime": "询价时间", "shippedPlace": "发货地", "originPort": "起始港", "unloadingPort": "目的港", "grossWeight": "货物毛重(KG)", "goodsVolume": "货物体积(m³)", "singleMaxWeight": "单件货最大重量(KG)", "isBrand": "是否品牌货", "ensureCabin": "保舱", "isValidity": "要求ETD", "shipmentDate": "发货日期", "packageType": "包装形式", "specialCargo": "特殊货物", "status": "状态", "actions": "操作"}, "filters": {"search": "搜索", "confirm": "确定", "reset": "重置"}, "status": {"quoted": "已报价", "unquoted": "未报价", "deal": "已成交", "nodeal": "未成交", "unknown": "未知"}, "specialCargo": {"none": "无"}, "pagination": {"total": "共 {{total}} 条记录"}, "messages": {"fetchListSuccess": "获取人工报价列表成功", "fetchListFailed": "获取人工报价列表失败"}, "quotationEditor": {"title": "人工报价单", "inquiryNumber": "询价单号", "buttons": {"saveQuotation": "保存报价单", "generateQuotation": "生成报价单", "cancelEdit": "取消编辑", "editContent": "编辑内容", "print": "打印", "export": "导出", "copy": "复制"}, "tooltips": {"print": "打印报价单", "export": "导出为PDF文件", "copy": "复制到剪贴板", "alignmentInfo": "报价单内容将按照等宽字体格式显示，保持对齐"}, "sections": {"quotationDetails": "报价单详情", "quotationInfo": "报价信息", "basicInfo": "基本信息", "priceInfo": "价格信息"}, "fields": {"quotationContent": "报价单内容", "validityDate": "有效期至", "airline": "航空公司", "route": "航线", "transitTime": "运输时间(天)", "schedule": "航班计划", "airFreight": "空运费(CNY/KG)", "chargeWeight": "计费重量(KG)", "terminalCharge": "航站楼费用(USD/KG)", "terminalChargeMin": "最低收费(USD)", "handleFee": "操作费(USD/票)", "customs": "海关费用(USD/票)", "pickupFee": "提货费(USD)", "airDocCharge": "空运单费(USD/票)", "ensCharge": "ENS费用(USD/票)", "otherCharges": "其他费用", "remarks": "备注"}, "placeholders": {"quotationContent": "请输入报价单内容", "airline": "请输入航空公司", "route": "例如: PVG-LAX", "transitTime": "例如: 1-3", "schedule": "例如: ********", "airFreight": "请输入空运费", "chargeWeight": "请输入计费重量", "terminalCharge": "请输入航站楼费用", "terminalChargeMin": "请输入最低收费", "handleFee": "请输入操作费", "customs": "请输入海关费用", "pickupFee": "请输入提货费", "airDocCharge": "请输入空运单费", "ensCharge": "请输入ENS费用", "otherCharges": "请输入其他费用", "remarks": "请输入备注信息"}, "validation": {"quotationContentRequired": "报价单内容不能为空", "validityDateRequired": "请选择有效期", "formValidationFailed": "请检查表单填写是否正确"}, "messages": {"saveSuccess": "报价单保存成功", "saveFailed": "保存失败", "exportSuccess": "报价单导出成功", "copySuccess": "报价单内容已复制到剪贴板", "copyFailed": "复制失败，请手动复制", "extractFieldsFailed": "提取字段失败"}}}, "organizationManagement": {"title": "组织管理", "tabs": {"userManagement": "用户管理", "departmentList": "部门列表"}, "userManagement": {"title": "用户列表", "newUser": "新建用户", "columns": {"email": "邮箱", "fullname": "姓名", "department": "所属部门", "identity": "身份", "phone": "电话", "status": "状态", "actions": "操作"}, "status": {"enabled": "启用", "disabled": "禁用", "systemUserCannotModify": "系统用户状态不可修改"}, "actions": {"edit": "编辑", "delete": "删除", "changePassword": "修改密码", "externalUserCannotEdit": "外部用户不可编辑", "externalUserCannotDelete": "外部用户不可删除", "externalUserCannotChangePassword": "外部用户不可修改密码"}, "deleteConfirm": {"title": "确定要删除此用户吗？", "description": "此操作不可逆，请谨慎操作。", "okText": "确定", "cancelText": "取消"}, "messages": {"userEnabled": "用户已启用", "userDisabled": "用户已禁用", "enableFailed": "启用失败", "disableFailed": "禁用失败", "deleteSuccess": "用户删除成功", "deleteFailed": "删除失败", "updateSuccess": "用户更新成功", "updateFailed": "更新失败", "addSuccess": "用户添加成功", "addFailed": "添加失败", "saveError": "保存用户出错", "enableError": "启用用户出错", "disableError": "禁用用户出错", "deleteError": "删除用户出错"}, "pagination": {"total": "共 {{total}} 条记录"}, "placeholders": {"searchUser": "搜索用户名、姓名或联系方式", "selectUserIdentity": "请选择用户身份"}, "userIdentity": {"0": "外部用户", "1": "询价业务员", "2": "价格业务员", "3": "询价部门主管", "4": "价格部门主管", "5": "超级管理员"}, "userFormModal": {"title": {"add": "添加用户", "edit": "编辑用户"}, "fields": {"email": "邮箱", "password": "密码", "fullname": "姓名", "department": "所属部门", "phone": "电话", "userIdentity": "用户身份", "status": "状态", "remarks": "备注"}, "placeholders": {"email": "请输入邮箱", "password": "请输入密码", "passwordEdit": "留空表示不修改密码", "fullname": "请输入姓名", "department": "请选择所属部门", "phone": "请输入电话", "remarks": "请输入备注信息"}, "validation": {"emailRequired": "请输入用户名", "emailInvalid": "请输入有效的邮箱地址", "emailMaxLength": "邮箱不能超过100个字符", "passwordRequired": "请输入密码", "passwordMaxLength": "密码不能超过50个字符", "fullnameMaxLength": "姓名不能超过100个字符", "departmentRequired": "请选择所属部门", "phoneMaxLength": "电话不能超过100个字符", "userIdentityRequired": "请选择用户身份", "remarksMaxLength": "备注不能超过200个字符"}, "tooltips": {"passwordEdit": "留空表示不修改密码"}, "status": {"enabled": "启用", "disabled": "禁用"}}}, "departmentManagement": {"title": "部门列表", "newDepartment": "新建部门", "columns": {"departmentId": "部门ID", "departmentName": "部门名称", "actions": "操作"}, "actions": {"edit": "编辑", "delete": "删除"}, "deleteConfirm": {"title": "确定要删除此部门吗？", "description": "此操作不可逆，请谨慎操作。", "okText": "确定", "cancelText": "取消"}, "messages": {"deleteSuccess": "部门删除成功", "addSuccess": "部门添加成功", "updateSuccess": "部门修改成功", "addFailed": "部门添加失败", "updateFailed": "部门修改失败"}, "departmentFormModal": {"title": {"add": "添加部门", "edit": "编辑部门"}, "fields": {"departmentId": "部门ID", "departmentName": "部门名称"}, "placeholders": {"departmentName": "请输入部门名称"}, "validation": {"departmentNameRequired": "请输入部门名称", "departmentNameMaxLength": "部门名称不能超过50个字符"}}}}, "quotationEditor": {"title": "航空运输报价单", "buttons": {"save": "保存报价单", "edit": "编辑内容", "cancel": "取消编辑", "close": "关闭", "print": "打印", "export": "导出", "copy": "复制", "reset": "重置", "confirmAdjustment": "确认调整", "cancelAdjustment": "取消调整"}, "sections": {"quotationDetails": "报价单详情", "quotationContent": "报价单内容", "priceAdjustment": "价格调整", "priceDetails": "价格详情", "additionalCharges": "附加费用", "additionalFees": "附加费用", "routeInfo": "航线信息", "cargoLimits": "货物限制", "priceInfo": "价格信息", "priceGradient": "价格梯度", "specialCharges": "特殊物品附加费", "packageCharges": "包装费用", "otherFeesAdjustment": "其他费用调整"}, "fields": {"department": "部门", "priceId": "价格ID", "airline": "航空公司", "originPort": "起始港", "destinationPort": "目的港", "originSchedules": "起始班次", "isTransfer": "是否中转", "transferPort": "中转港", "transferSchedules": "中转班次", "minDensity": "最小密度", "maxDensity": "最大密度", "lengthLimit": "长度限制", "widthLimit": "宽度限制", "heightLimit": "高度限制", "canEnsureCabin": "是否可保舱", "cabinPrice": "保舱价格", "smallCargoFee": "小货操作费", "totalPrice": "总价", "originalPrice": "原始总价", "adjustedPrice": "调整后总价", "profit": "利润", "mPrice": "M价", "nPrice": "N价", "q45Price": "Q45价格", "q100Price": "Q100价格", "q300Price": "Q300价格", "q500Price": "Q500价格", "q1000Price": "Q1000价格", "terminalCharge": "终端费用", "handleFee": "处理费", "customsFee": "报关费", "pickingFee": "提货费", "docCharge": "文件费"}, "priceTypes": {"mPrice": "M价", "nPrice": "N价", "q45Price": "Q45价格", "q100Price": "Q100价格", "bulkPrice": "大货价格", "standardPrice": "标准价格", "basePrice": "基础价格", "above45kg": "45kg以上", "above100kg": "100kg以上", "above300kg": "300kg以上"}, "priceLabels": {"standard": "标准价", "base": "基础价", "standardTier": "标准", "segment": "分段", "q300": "Q300", "q500": "Q500", "q1000": "Q1000"}, "booleanValues": {"yes": "是", "no": "否"}, "units": {"cny": "CNY", "cnyPerKg": "CNY/KG", "kgPerCubicMeter": "kg/m³", "days": "天"}, "placeholders": {"quotationContent": "请输入报价单内容"}, "tooltips": {"quotationFormat": "报价单内容将按照等宽字体格式显示，保持对齐"}, "validation": {"quotationContentRequired": "报价单内容不能为空"}, "messages": {"saveSuccess": "报价单保存成功", "copySuccess": "报价单内容已复制到剪贴板", "copyFailed": "复制失败，请手动复制", "validationFailed": "请检查表单填写是否正确", "resetSuccess": "价格已重置为原始值", "priceAdjustmentConfirmed": "价格调整已确认", "priceAdjustmentCanceled": "价格调整已取消"}, "additionalFees": {"title": "额外费用", "terminalCharge": "终端费用", "handleFee": "处理费", "customsFee": "报关费", "pickupFee": "提货费", "docCharge": "文件费", "ensCharge": "申报费", "brandCost": "品牌处理费", "carPaking": "停车费", "smallCargoFee": "小货操作费", "none": "无"}, "otherFees": {"title": "其他费用", "selectFees": "选择费用项目", "feeName": "费用名称", "billingMethod": "计费方式", "orderFees": "每单费用", "weightFees": "每kg费用", "minCharges": "最低收费", "quantity": "数量", "isDefault": "默认收费", "totalAmount": "总金额", "perOrder": "按单计费", "perKg": "按重量计费", "confirmSelection": "确认选择", "cancelSelection": "取消选择", "noFeesSelected": "未选择任何费用项目", "feesSelected": "已选择 {count} 项费用", "defaultFeesAutoSelected": "默认收费项已自动勾选", "adjustmentConfirmed": "其他费用调整已确认", "adjustmentCanceled": "其他费用调整已取消"}, "quotationTemplate": {"airline": "Airline（航空公司）", "route": "Route（路线）", "transitTime": "T/T（时效）", "schedule": "Schedule（班次频率）", "airFreight": "A/F (运费单价)", "chargeableWeight": "CW（计费重）", "additionalFees": "额外费用"}}, "dataManagement": {"airlineManagement": {"title": "航司管理", "newAirline": "新建航司", "columns": {"cnAirlineName": "航司中文名称", "enAirlineName": "航司英文名称", "actions": "操作"}, "actions": {"edit": "编辑", "delete": "删除"}, "deleteConfirm": {"title": "删除航司", "description": "此操作将删除该航司,是否继续?", "okText": "确定", "cancelText": "取消"}, "messages": {"deleteSuccess": "航司删除成功", "addSuccess": "航司添加成功", "updateSuccess": "航司修改成功", "addFailed": "航司添加失败", "updateFailed": "航司修改失败"}, "modal": {"title": {"add": "新建航司", "edit": "编辑航司"}, "fields": {"id": "ID", "cnAirlineName": "航司中文名称", "enAirlineName": "航司英文名称"}, "placeholders": {"cnAirlineName": "请输入航司中文名称", "enAirlineName": "请输入航司英文名称"}, "validation": {"cnAirlineNameRequired": "请输入航司中文名称!", "cnAirlineNameMaxLength": "航司中文名称不能超过100个字符!", "enAirlineNameRequired": "请输入航司英文名称!", "enAirlineNameMaxLength": "航司英文名称不能超过100个字符!"}}}, "harborManagement": {"title": "港口管理", "newHarbor": "新建港口", "columns": {"cnPortName": "港口中文名称", "enPortName": "行业专用(英文)名称", "actions": "操作"}, "actions": {"edit": "编辑", "delete": "删除"}, "deleteConfirm": {"title": "删除港口", "description": "此操作将删除该港口,是否继续?", "okText": "确定", "cancelText": "取消"}, "messages": {"deleteSuccess": "港口删除成功", "addSuccess": "港口添加成功", "updateSuccess": "港口修改成功", "addFailed": "港口添加失败", "updateFailed": "港口修改失败"}, "modal": {"title": {"add": "新建港口", "edit": "编辑港口"}, "fields": {"id": "ID", "cnPortName": "港口中文名称", "enPortName": "行业专用(英文)名称"}, "placeholders": {"cnPortName": "请输入港口中文名称", "enPortName": "请输入行业专用(英文)名称"}, "validation": {"cnPortNameRequired": "请输入港口中文名称", "cnPortNameMaxLength": "港口中文名称不能超过100个字符", "enPortNameRequired": "请输入行业专用(英文)名称", "enPortNameMaxLength": "行业专用(英文)名称不能超过100个字符"}}}, "packageTypeManagement": {"title": "包装类型管理", "newPackageType": "新建包装类型", "columns": {"id": "ID", "packageName": "包装名称", "actions": "操作"}, "actions": {"edit": "编辑", "delete": "删除"}, "deleteConfirm": {"title": "删除包装类型", "description": "此操作将删除该包装类型,是否继续?", "okText": "确定", "cancelText": "取消"}, "messages": {"deleteSuccess": "包装类型删除成功", "addSuccess": "包装类型添加成功", "updateSuccess": "包装类型修改成功", "addFailed": "包装类型添加失败", "updateFailed": "包装类型修改失败"}, "modal": {"title": {"add": "新建包装类型", "edit": "编辑包装类型"}, "fields": {"id": "ID", "packageName": "包装名称"}, "placeholders": {"packageName": "请输入包装名称"}, "validation": {"packageNameRequired": "请输入包装名称!", "packageNameMaxLength": "包装名称不能超过100个字符!"}}}, "specialItemsManagement": {"title": "特殊物品管理", "newSpecialItem": "新建特殊物品", "columns": {"id": "ID", "specialItemsName": "特殊物品名称", "actions": "操作"}, "actions": {"edit": "编辑", "delete": "删除"}, "deleteConfirm": {"title": "删除特殊物品", "description": "此操作将删除该特殊物品,是否继续?", "okText": "确定", "cancelText": "取消"}, "messages": {"deleteSuccess": "特殊物品删除成功", "addSuccess": "特殊物品添加成功", "updateSuccess": "特殊物品修改成功", "addFailed": "特殊物品添加失败", "updateFailed": "特殊物品修改失败"}, "modal": {"title": {"add": "新建特殊物品", "edit": "编辑特殊物品"}, "fields": {"id": "ID", "specialItemsName": "特殊物品名称"}, "placeholders": {"specialItemsName": "请输入特殊物品名称"}, "validation": {"specialItemsNameRequired": "请输入特殊物品名称!", "specialItemsNameMaxLength": "特殊物品名称不能超过100个字符!"}}}, "shipmentPlaceManagement": {"title": "发货地管理", "newShipmentPlace": "新建发货地", "columns": {"shipmentPlace": "发货地", "enPlace": "发货地英文(拼音)", "shipmentProvince": "所在省份", "enProvince": "省份英文(拼音)", "actions": "操作"}, "actions": {"edit": "编辑", "delete": "删除"}, "deleteConfirm": {"title": "删除发货地", "description": "此操作将删除该发货地,是否继续?", "okText": "确定", "cancelText": "取消"}, "messages": {"deleteSuccess": "发货地删除成功", "addSuccess": "发货地添加成功", "updateSuccess": "发货地修改成功", "addFailed": "发货地添加失败", "updateFailed": "发货地修改失败"}, "modal": {"title": {"add": "新建发货地", "edit": "编辑发货地"}, "fields": {"id": "ID", "shipmentPlace": "发货地", "enPlace": "发货地英文(拼音)", "shipmentProvince": "所在省份", "enProvince": "省份英文(拼音)"}, "placeholders": {"shipmentPlace": "请输入发货地", "enPlace": "请输入发货地英文(拼音)", "shipmentProvince": "请输入所在省份", "enProvince": "请输入省份英文(拼音)"}, "validation": {"shipmentPlaceRequired": "请输入发货地!", "shipmentPlaceMaxLength": "发货地不能超过100个字符!", "enPlaceRequired": "请输入发货地英文(拼音)!", "enPlaceMaxLength": "发货地英文(拼音)不能超过100个字符!", "shipmentProvinceRequired": "请输入所在省份!", "shipmentProvinceMaxLength": "所在省份不能超过100个字符!", "enProvinceRequired": "请输入省份英文(拼音)!", "enProvinceMaxLength": "省份英文(拼音)不能超过100个字符!"}}}, "supplierManagement": {"title": "供应商管理", "newSupplier": "新建供应商", "columns": {"supplierName": "供应商名称", "actions": "操作"}, "actions": {"edit": "编辑", "delete": "删除"}, "deleteConfirm": {"title": "删除供应商", "description": "此操作将删除该供应商,是否继续?", "okText": "确定", "cancelText": "取消"}, "messages": {"deleteSuccess": "供应商删除成功", "addSuccess": "供应商添加成功", "updateSuccess": "供应商修改成功", "addFailed": "供应商添加失败", "updateFailed": "供应商修改失败"}, "modal": {"title": {"add": "新建供应商", "edit": "编辑供应商"}, "fields": {"id": "ID", "supplierName": "供应商名称"}, "placeholders": {"supplierName": "请输入供应商名称"}, "validation": {"supplierNameRequired": "请输入供应商名称!", "supplierNameMaxLength": "供应商名称不能超过100个字符!"}}}, "miscellaneousFeeManagement": {"title": "其他费用管理", "newMiscellaneousFee": "新建其他费用", "columns": {"miscellaneousFeeName": "其他费用名称", "actions": "操作"}, "actions": {"edit": "编辑", "delete": "删除"}, "deleteConfirm": {"title": "删除其他费用", "description": "此操作将删除该其他费用,是否继续?", "okText": "确定", "cancelText": "取消"}, "messages": {"deleteSuccess": "其他费用删除成功", "addSuccess": "其他费用添加成功", "updateSuccess": "其他费用修改成功", "addFailed": "其他费用添加失败", "updateFailed": "其他费用修改失败"}, "modal": {"title": {"add": "新建其他费用", "edit": "编辑其他费用"}, "fields": {"id": "ID", "miscellaneousFeeName": "其他费用名称"}, "placeholders": {"miscellaneousFeeName": "请输入其他费用名称"}, "validation": {"miscellaneousFeeNameRequired": "请输入其他费用名称!", "miscellaneousFeeNameMaxLength": "其他费用名称不能超过100个字符!"}}}}, "quickAccess": {"title": "基础数据快速访问：", "goTo": "前往"}}