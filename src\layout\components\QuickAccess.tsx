import React, { useMemo } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { routerList } from "@/config/router";
import * as Icon from "@ant-design/icons";
import { Tooltip } from "antd";
import { DatabaseOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import "./index.less";
import { useAppSelector } from "@/store/hooks";

// 定义 IconType，确保 icon 只能是合法的 Ant Design 图标名称
type IconType = keyof typeof Icon;

const QuickAccess: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const currentPath = location.pathname;
  const { user } = useAppSelector((state) => state.user);

  // 动态获取 icon
  const iconToElement = (name: IconType): React.ReactElement | null => {
    const IconComponent = Icon[name] as React.ComponentType<any>;
    return IconComponent ? <IconComponent /> : null;
  };

  // 获取基础数据菜单及其子菜单
  const dataManagementMenu = useMemo(() => {
    return routerList.find((item) => item.key === "/data_management");
  }, []);

  // 确定当前应该显示哪个菜单的快速访问
  const currentMenuType = useMemo(() => {
    // 获取基础数据子菜单路径
    const dataSubMenuPaths =
      dataManagementMenu?.children?.map((item) => item.path) || [];

    // 检查当前路径是否在基础数据子菜单中
    const isInDataMenu = dataSubMenuPaths.some(
      (path) => currentPath === path || currentPath.startsWith(path + "/")
    );

    // 根据用户权限过滤
    if (
      user?.useridentity === 2 ||
      user?.useridentity === 4 ||
      user?.useridentity === 5
    ) {
      // 用户角色为2、4或5的用户可以访问基础数据管理
      if (isInDataMenu) return "data";
    }
    return null;
  }, [currentPath, dataManagementMenu, user]);

  // 根据当前菜单类型获取子菜单项
  const subMenuItems = useMemo(() => {
    if (currentMenuType === "data") {
      // 对于用户角色2、4和5，显示允许的六个子菜单
      if (
        user?.useridentity === 2 ||
        user?.useridentity === 4 ||
        user?.useridentity === 5
      ) {
        const allowedKeys = [
          "/airline_management",
          "/harbor_management",
          "/package_type_management",
          "/special_items_management",
          "/shipment_place_management",
          "/supplier_management",
          "/miscellaneous_fee_management",
        ];
        return (
          dataManagementMenu?.children?.filter((item) =>
            allowedKeys.includes(item.key)
          ) || []
        );
      } else {
        return dataManagementMenu?.children || [];
      }
    }
    return [];
  }, [currentMenuType, dataManagementMenu, user]);

  // 如果没有子菜单或当前不在任何需要显示快速访问的菜单中，则不显示快速访问栏
  if (subMenuItems.length === 0 || !currentMenuType) {
    return null;
  }

  return (
    <div className="quick-access-bar">
      <div className="quick-access-title">
        <DatabaseOutlined style={{ marginRight: 6, color: "#1797e1" }} />
        <span>{t("quickAccess.title")}</span>
      </div>
      <div className="quick-access-items">
        {subMenuItems.map((item) => (
          <Tooltip
            key={item.key}
            title={`${t("quickAccess.goTo")}${item.labelKey ? t(item.labelKey) : item.label}`}
            placement="bottom"
          >
            <div
              className={`quick-access-item ${currentPath === item.path ? "active" : ""}`}
              onClick={() => navigate(item.path)}
            >
              {item.icon && iconToElement(item.icon)}
              <span>{item.labelKey ? t(item.labelKey) : item.label}</span>
            </div>
          </Tooltip>
        ))}
      </div>
    </div>
  );
};

export default QuickAccess;
