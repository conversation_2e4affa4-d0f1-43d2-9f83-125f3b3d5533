import { get, post, del } from "@/utils/request";
import type {
  Airline,
  Port,
  PackageType,
  SpecialItem,
  ShipmentPlace,
  Supplier,
  MiscellaneousFee,
} from "@/store/slices/baseDataSlice";

// 定义用于新增操作的类型（不包含ID字段）
export type CreateAirlineData = Omit<Airline, "airlineid">;
export type CreatePortData = Omit<Port, "portid">;
export type CreatePackageTypeData = Omit<PackageType, "packagetypeid">;
export type CreateSpecialItemData = Omit<SpecialItem, "specialitemsid">;
export type CreateShipmentPlaceData = Omit<ShipmentPlace, "placeid">;
export type CreateSupplierData = Omit<Supplier, "supplierid">;
export type CreateMiscellaneousFeeData = Omit<MiscellaneousFee, "otherfeesid">;

// 定义用于删除操作的ID类型
export interface DeleteAirlineParams {
  airlineid: number;
}

export interface DeletePortParams {
  portid: number;
}

export interface DeletePackageTypeParams {
  packagetypeid: number;
}

export interface DeleteSpecialItemParams {
  specialitemsid: number;
}

export interface DeleteShipmentPlaceParams {
  placeid: number;
}

export interface DeleteSupplierParams {
  supplierid: number;
}

export interface DeleteMiscellaneousFeeParams {
  otherfeesid: number;
}

// 航司管理相关接口
export const getAllAirlineCompany = () => {
  return get("/getAllAirlineCompany");
};

export const addAirlineCompany = (data: CreateAirlineData) => {
  return post("/addAirlineCompany", data);
};

export const updateAirlineCompany = (data: Airline) => {
  return post("/updateAirlineCompany", data);
};

export const delAirlineCompany = (data: DeleteAirlineParams) => {
  return del("/delAirlineCompany", data);
};

// 港口管理相关接口
export const getAllPort = () => {
  return get("/getAllPort");
};

export const addPort = (data: CreatePortData) => {
  return post("/addPort", data);
};

export const updatePort = (data: Port) => {
  return post("/updatePort", data);
};

export const delPort = (data: DeletePortParams) => {
  return del("/delPort", data);
};

// 包装类型管理相关接口
export const getAllPackageType = () => {
  return get("/getAllPackageType");
};

export const addPackageType = (data: CreatePackageTypeData) => {
  return post("/addPackageType", data);
};

export const updatePackageType = (data: PackageType) => {
  return post("/updatePackageType", data);
};

export const delPackageType = (data: DeletePackageTypeParams) => {
  return del("/delPackageType", data);
};

// 特殊货物管理相关接口
export const getAllSpecialitems = () => {
  return get("/getAllSpecialitems");
};

export const addSpecialitems = (data: CreateSpecialItemData) => {
  return post("/addSpecialitems", data);
};

export const updateSpecialitems = (data: SpecialItem) => {
  return post("/updateSpecialitems", data);
};

export const delSpecialitems = (data: DeleteSpecialItemParams) => {
  return del("/delSpecialitems", data);
};

// 发货地管理相关接口
export const getAllShipmentPlace = () => {
  return get("/getAllShipmentPlace");
};

export const addShipmentPlace = (data: CreateShipmentPlaceData) => {
  return post("/addShipmentPlace", data);
};

export const updateShipmentPlace = (data: ShipmentPlace) => {
  return post("/updateShipmentPlace", data);
};

export const delShipmentPlace = (data: DeleteShipmentPlaceParams) => {
  return del("/delShipmentPlace", data);
};

// 供应商管理相关接口
export const getAllSupplier = () => {
  return get("/getAllSupplier");
};

export const addSupplier = (data: CreateSupplierData) => {
  return post("/addSupplier", data);
};

export const updateSupplier = (data: Supplier) => {
  return post("/updateSupplier", data);
};

export const delSupplier = (data: DeleteSupplierParams) => {
  return del("/delSupplier", data);
};

// 其他费用管理相关接口
export const getAllOtherfeesName = () => {
  return get("/getAllOtherfeesName");
};
export const addOtherfeesName = (data: CreateMiscellaneousFeeData) => {
  return post("/addOtherfeesName", data);
};

export const updateOtherfeesName = (data: MiscellaneousFee) => {
  return post("/updateOtherfeesName", data);
};

export const delOtherfeesName = (data: DeleteMiscellaneousFeeParams) => {
  return del("/delOtherfeesName", data);
};
