import * as Icon from "@ant-design/icons";

// 定义 IconType，确保 icon 只能是合法的 Ant Design 图标名称
type IconType = keyof typeof Icon;

export interface MenuItem {
  key: string;
  path: string;
  icon?: IconType;
  label: string;
  labelKey?: string; // 用于国际化的key
  authenticate?: boolean;
  children?: MenuItem[];
}

export const routerList: MenuItem[] = [
  {
    key: "/quotation",
    label: "询价管理",
    labelKey: "menu.quotation",
    path: "/quotation",
    icon: "FileTextOutlined",
  },
  {
    key: "/supply_price",
    label: "供应价格筛选",
    labelKey: "menu.supplyPrice",
    path: "/supply_price",
    icon: "ShoppingOutlined",
  },
  // {
  //   key: "/personal_quotation",
  //   label: "报价查询",
  //   labelKey: "menu.personalQuotation",
  //   path: "/personal_quotation",
  //   icon: "RocketOutlined",
  // },
  {
    key: "/ai_quotation",
    label: "AI智能报价",
    labelKey: "menu.aiQuotation",
    path: "/ai_quotation",
    icon: "RobotOutlined",
  },
  {
    key: "/intelligenty_quotation",
    label: "智能报价",
    labelKey: "menu.IntelligentyQuotation",
    path: "/intelligenty_quotation",
    icon: "ThunderboltOutlined",
  },
  {
    key: "/international_price",
    label: "国际价格管理",
    labelKey: "menu.internationalPrice",
    path: "/international_price",
    icon: "GlobalOutlined",
  },
  {
    key: "/domestic_price",
    label: "国内价格管理",
    labelKey: "menu.domesticPrice",
    path: "/domestic_price",
    icon: "DollarOutlined",
  },
  {
    key: "/manual_quotation",
    label: "特殊报价管理",
    labelKey: "menu.manualQuotation",
    path: "/manual_quotation",
    icon: "FormOutlined",
  },
  {
    key: "/data_management",
    label: "基础数据",
    labelKey: "menu.dataManagement",
    path: "/data_management",
    icon: "DatabaseOutlined",
    children: [
      {
        key: "/airline_management",
        label: "航司管理",
        labelKey: "menu.airlineManagement",
        path: "/airline_management",
        icon: "RocketOutlined",
      },
      {
        key: "/harbor_management",
        label: "港口管理",
        labelKey: "menu.harborManagement",
        path: "/harbor_management",
        icon: "CompassOutlined",
      },
      {
        key: "/package_type_management",
        label: "包装类型管理",
        labelKey: "menu.packageTypeManagement",
        path: "/package_type_management",
        icon: "InboxOutlined",
      },
      {
        key: "/special_items_management",
        label: "特殊物品管理",
        labelKey: "menu.specialItemsManagement",
        path: "/special_items_management",
        icon: "TagOutlined",
      },
      {
        key: "/shipment_place_management",
        label: "发货地管理",
        labelKey: "menu.shipmentPlaceManagement",
        path: "/shipment_place_management",
        icon: "EnvironmentOutlined",
      },
      {
        key: "/supplier_management",
        label: "供应商管理",
        labelKey: "menu.supplierManagement",
        path: "/supplier_management",
        icon: "ShopOutlined",
      },
      {
        key: "/miscellaneous_fee_management",
        label: "其他费用管理",
        labelKey: "menu.miscellaneousFeeManagement",
        path: "/miscellaneous_fee_management",
        icon: "ShopOutlined",
      },
    ],
  },
  {
    key: "/organization_manage",
    label: "组织管理",
    labelKey: "menu.organizationManage",
    path: "/organization_manage",
    icon: "TeamOutlined",
  },
];
