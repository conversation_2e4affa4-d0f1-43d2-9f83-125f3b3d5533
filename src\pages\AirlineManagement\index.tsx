import React, { useState, useEffect } from "react";
import "./index.less";
import TableCom from "@/components/TableCom";
import { Space, Button, Form, Popconfirm, Tooltip, message } from "antd";
import type { TableProps } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { delAirlineCompany } from "@/services/baseDataService";
import ActionModal from "./components/ActionModal";
import useBaseData from "@/hooks/useBaseData";

const AirlineManagement: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [method, setMethod] = useState<string>("");

  // 使用自定义Hook获取航司数据
  const { airlines: data, loadAirlines } = useBaseData();

  useEffect(() => {
    loadAirlines();
  }, []);

  const columns: TableProps<any>["columns"] = [
    // {
    //   title: "ID",
    //   dataIndex: "airlineid",
    //   key: "airlineid",
    //   width: 80,
    // },
    {
      title: t("dataManagement.airlineManagement.columns.cnAirlineName"),
      dataIndex: "cnairlinename",
      key: "cnairlinename",
    },
    {
      title: t("dataManagement.airlineManagement.columns.enAirlineName"),
      dataIndex: "enairlinename",
      key: "enairlinename",
    },
    {
      title: t("dataManagement.airlineManagement.columns.actions"),
      key: "action",
      render: (_, record) => (
        <Space size="middle" className="operation-buttons">
          <Tooltip title={t("dataManagement.airlineManagement.actions.edit")}>
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              className="edit-button"
              size="small"
            />
          </Tooltip>
          <Tooltip title={t("dataManagement.airlineManagement.actions.delete")}>
            <Popconfirm
              title={t("dataManagement.airlineManagement.deleteConfirm.title")}
              description={t(
                "dataManagement.airlineManagement.deleteConfirm.description"
              )}
              onConfirm={() => handleDel(record)}
              onCancel={handleCancel}
              okText={t(
                "dataManagement.airlineManagement.deleteConfirm.okText"
              )}
              cancelText={t(
                "dataManagement.airlineManagement.deleteConfirm.cancelText"
              )}
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                className="delete-button"
                size="small"
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    showModal();
    setMethod("add");
    form.resetFields();
  };

  const handleEdit = (record: any) => {
    setMethod("edit");
    showModal();
    form.setFieldsValue(record);
  };

  const handleDel = async (record: any) => {
    const res = await delAirlineCompany({ airlineid: record?.airlineid });
    const { data } = res;
    if (data.resultCode === 200) {
      // 刷新页面数据
      loadAirlines(true); // 强制刷新数据
      message.success(
        t("dataManagement.airlineManagement.messages.deleteSuccess")
      );
    } else {
      message.error(data.message);
    }
  };

  const handleCancel = () => {
    // 取消删除操作
  };

  const showModal = () => {
    setIsModalOpen(true);
  };

  const filterRender = () => {
    return (
      <div className="filter_box">
        <div className="title">
          {t("dataManagement.airlineManagement.title")}
        </div>
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            {t("dataManagement.airlineManagement.newAirline")}
          </Button>
        </Space>
      </div>
    );
  };

  return (
    <div className="airline-management">
      <TableCom<any>
        columns={columns}
        data={data}
        filterRender={filterRender}
      />
      <ActionModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        method={method}
        form={form}
        queryAirlineList={() => loadAirlines(true)}
      />
    </div>
  );
};

export default AirlineManagement;
